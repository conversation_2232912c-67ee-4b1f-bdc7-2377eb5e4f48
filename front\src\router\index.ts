import { route } from 'quasar/wrappers';
import {
  createMemoryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
  NavigationGuardNext,
  RouteLocationNormalizedGeneric,
} from 'vue-router';
import { useAuthStore } from 'src/stores/auth';
import type { AuthStore } from 'src/stores/auth';
import AUTH_API from 'src/api/modules/auth';

import routes from './routes';

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

export default route(function (/* { store, ssrContext } */) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : process.env.VUE_ROUTER_MODE === 'history'
    ? createWebHistory
    : createWebHashHistory;

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  // 檢查是否需要身份驗證
  const checkRequiresAuth = (to: RouteLocationNormalizedGeneric) => {
    return to.matched.some((record) => record.meta.requiresAuth);
  };

  // 檢查是否需要管理員權限
  const checkRequiresAdmin = (to: RouteLocationNormalizedGeneric) => {
    return to.matched.some((record) => record.meta.requiresAdmin);
  };

  // 導向登入頁面
  const redirectToLogin = (
    to: RouteLocationNormalizedGeneric,
    next: NavigationGuardNext
  ) => {
    next({ path: '/login', query: { redirect: to.fullPath } });
  };

  // 初始化 auth store
  const initializeAuth = (authStore: AuthStore) => {
    if (!authStore.accessToken) {
      authStore.initializeFromStorage();
    }
  };

  // 處理 token 刷新
  const handleTokenRefresh = async (
    authStore: AuthStore,
    to: RouteLocationNormalizedGeneric,
    next: NavigationGuardNext
  ) => {
    try {
      const response = await AUTH_API.refreshToken();
      authStore.updateToken(response.data);
      next();
      return true;
    } catch (error) {
      authStore.logout();
      redirectToLogin(to, next);
      return false;
    }
  };

  // 驗證管理員權限
  const validateAdminAccess = (
    authStore: AuthStore,
    next: NavigationGuardNext
  ) => {
    if (!authStore.isAuthenticated || !authStore.isAdmin()) {
      next({ path: '/index' });
      return false;
    }
    return true;
  };

  Router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore();
    const requiresAuth = checkRequiresAuth(to);
    const requiresAdmin = checkRequiresAdmin(to);

    initializeAuth(authStore);

    // 確認是否需要身份驗證
    if (requiresAuth) {
      if (!authStore.isAuthenticated) {
        redirectToLogin(to, next);
        return;
      }

      if (authStore.isTokenExpired && !authStore.isRefreshTokenExpired) {
        const refreshSuccess = await handleTokenRefresh(authStore, to, next);
        if (!refreshSuccess) return;
      }

      if (authStore.isRefreshTokenExpired) {
        authStore.logout();
        redirectToLogin(to, next);
        return;
      }
    }

    // 確認是否需要管理員權限
    if (requiresAdmin) {
      if (!validateAdminAccess(authStore, next)) {
        return;
      }
    }

    next();
  });

  return Router;
});
