import { defineStore } from 'pinia';
import { useRouter } from 'vue-router';
import { Notify } from 'quasar';
import { LoginResponse, TokenResponse, User } from '@/api/modules/auth';

// WebSocket連接
let ws: WebSocket;

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null as User | null,
    accessToken: '',
    tokenExpiry: null as Date | null,
    refreshToken: '',
    refreshTokenExpiry: null as Date | null,
  }),
  persist: true,
  getters: {
    userInfo: (state) => state.user,
    isAuthenticated: (state) => !!state.accessToken,
    isTokenExpired: (state) => {
      if (!state.tokenExpiry) {
        return true;
      }

      return new Date() >= state.tokenExpiry;
    },
    isRefreshTokenExpired: (state) => {
      if (!state.refreshTokenExpiry) {
        return true;
      }

      return new Date() >= state.refreshTokenExpiry;
    },
  },
  actions: {
    login(data: LoginResponse) {
      this.user = data.user;
      this.updateToken(data);
    },
    getTokenClaims() {
      const accessToken = this.accessToken;
      if (!accessToken) {
        return null;
      }

      try {
        // 解碼 accessToken
        const base64Url = accessToken.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const claims = JSON.parse(atob(base64));

        return {
          user_id: claims.user_id,
          is_admin: claims.is_admin,
        };
      } catch {
        return null;
      }
    },
    isAdmin() {
      const claims = this.getTokenClaims();
      return claims?.is_admin || false;
    },
    updateToken(data: TokenResponse) {
      this.accessToken = data.access_token;
      this.tokenExpiry = data.expires_at;
      this.refreshToken = data.refresh_token;
      this.refreshTokenExpiry = data.refresh_expires_at;

      localStorage.setItem('accessToken', data.access_token);
      localStorage.setItem('tokenExpiry', data.expires_at.toString());
      localStorage.setItem('refreshToken', data.refresh_token);
      localStorage.setItem(
        'refreshTokenExpiry',
        data.refresh_expires_at.toString()
      );
    },
    logout() {
      this.accessToken = '';
      this.tokenExpiry = null;
      this.refreshToken = '';
      this.refreshTokenExpiry = null;
      this.user = null;

      localStorage.removeItem('accessToken');
      localStorage.removeItem('tokenExpiry');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('refreshTokenExpiry');
    },
    initializeFromStorage() {
      const accessToken = localStorage.getItem('accessToken');
      const refreshToken = localStorage.getItem('refreshToken');
      const tokenExpiry = localStorage.getItem('tokenExpiry');
      const refreshTokenExpiry = localStorage.getItem('refreshTokenExpiry');

      if (accessToken && refreshToken && tokenExpiry && refreshTokenExpiry) {
        this.accessToken = accessToken;
        this.tokenExpiry = new Date(tokenExpiry);
        this.refreshToken = refreshToken;
        this.refreshTokenExpiry = new Date(refreshTokenExpiry);
      }
    },
    setupWebSocket() {
      // 關閉現有連接
      if (ws) {
        ws.close();
      }

      // Establish new WebSocket connection
      const token = localStorage.getItem('token');
      const wsUrl = process.env.WS_URL as string;
      ws = new WebSocket(`${wsUrl}?token=${token}`);

      ws.onmessage = (event: MessageEvent) => {
        const data = JSON.parse(event.data);

        if (data.type === 'forced_logout') {
          Notify.create({
            type: 'warning',
            message: data.message || '您的帳號已在另一個設備登入',
          });

          // Execute logout operation
          this.logout();

          // Redirect to login page
          // Note: You might need to use router from your app
          const router = useRouter();
          router.push('/login');
        }
      };
    },
  },
});

export type AuthStore = ReturnType<typeof useAuthStore>;
