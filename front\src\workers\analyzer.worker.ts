import {
  AnalysisConfig,
  DrawResult,
  StatResult,
  ProgressInfo,
  Occurrence,
} from '@/models/types';

// Worker 事件處理
self.onmessage = (e) => {
  const { type, data } = e.data;

  if (type === 'init') {
    const results: DrawResult[] = JSON.parse(data.results);
    const config: AnalysisConfig = JSON.parse(data.config);
    analyzeLotto(results, config);
  }
};

function analyzeLotto(results: DrawResult[], config: AnalysisConfig) {
  const statResults = new Map<string, StatResult>();
  const occurrenceResults = new Map<string, Occurrence>(); // 用來統計每個號碼的出現次數
  const hitDetails = new Map<string, string[]>(); // 追蹤每個組合命中的期號列表

  const predictIndex = results.length - 1 + config.lookAheadCount;
  const totalProgress = estimateTotalProgress(results, config);
  let progress = 0;

  function processBatch(startIndex: number) {
    const batchSize = 10000; // 控制每次處理的筆數，避免長時間佔用 Worker
    const endIndex = Math.min(results.length - config.lookAheadCount, startIndex + batchSize);

    // 先計算 Occurrence 統計 - 確保完整處理所有組合
    for (let i = startIndex; i < endIndex; i++) {
      // 第一組號碼組合
      const firstGroupsGen = getCombinationsGenerator(
        results[i].numbers,
        config.firstGroupSize
      );
      const firstGroups = Array.from(firstGroupsGen);

      // j 迴圈需要處理完整範圍，不受批次限制
      for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
        // 第二組號碼組合
        const secondGroupsGen = getCombinationsGenerator(
          results[j].numbers,
          config.secondGroupSize
        );
        const secondGroups = Array.from(secondGroupsGen);
        const gap = j - i;

        // k 迴圈也需要處理完整範圍，不受批次限制
        for (let k = j + 1; k - j <= config.maxRange; k++) {
          if (k > predictIndex) break;

          const targetGap = k - j;

          // 計算 Occurrence 統計
          for (const firstGroup of firstGroups) {
            for (const secondGroup of secondGroups) {
              const key = `${firstGroup.join(',')}-${secondGroup.join(
                ','
              )}-${gap}-${targetGap}`;

              const occurrence: Occurrence = occurrenceResults.get(key) || {
                count: 0,
                periods: [],
                isPredict: false,
              };

              if (k < results.length) {
                occurrence.count++;
                occurrence.periods.push({
                  firstPeriod: results[i].period,
                  secondPeriod: results[j].period,
                  targetPeriod: results[k].period,
                });
              } else if (k === predictIndex) {
                occurrence.periods.push({
                  firstPeriod: results[i].period,
                  secondPeriod: results[j].period,
                });

                occurrence.isPredict = true;
              }
              occurrenceResults.set(key, occurrence);
            }
          }
        }
      }
    }

    // 處理預測結果 - 確保完整處理所有組合
    for (let i = startIndex; i < endIndex; i++) {
      // 第一組號碼組合
      const firstGroupsGen = getCombinationsGenerator(
        results[i].numbers,
        config.firstGroupSize
      );
      const firstGroups = Array.from(firstGroupsGen);

      // j 迴圈需要處理完整範圍，不受批次限制
      for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
        // 第二組號碼組合
        const secondGroupsGen = getCombinationsGenerator(
          results[j].numbers,
          config.secondGroupSize
        );
        const secondGroups = Array.from(secondGroupsGen);
        const gap = j - i;

        // k 迴圈也需要處理完整範圍，不受批次限制
        for (let k = j + 1; k - j <= config.maxRange; k++) {
          if (k > predictIndex) break;

          const targetGap = k - j;

          let targetGroups: number[][] = [];
          if (k < results.length) {
            const targetGroupsGen = getCombinationsGenerator(
              results[k].numbers,
              config.targetGroupSize
            );
            targetGroups = Array.from(targetGroupsGen);
          }

          // 處理預測結果
          for (const firstGroup of firstGroups) {
            for (const secondGroup of secondGroups) {
              const key = `${firstGroup.join(',')}-${secondGroup.join(
                ','
              )}-${gap}-${targetGap}`;

              const occurrence: Occurrence = occurrenceResults.get(key) || {
                count: 0,
                periods: [],
                isPredict: false,
              };

              if (!occurrence.isPredict) continue;

              if (k < results.length) {
                for (const targetGroup of targetGroups) {
                  const key2 = `${firstGroup.join(',')}-${secondGroup.join(
                    ','
                  )}-${gap}-${targetGap}-${targetGroup.join(',')}`;
                  const stat = statResults.get(key2);

                  if (stat) {
                    stat.targetMatches++;
                  } else {
                    const newStat: StatResult = {
                      firstNumbers: firstGroup,
                      secondNumbers: secondGroup,
                      targetNumbers: targetGroup,
                      gap: gap,
                      targetGap: targetGap,
                      targetMatches: 1,
                      targetProbability: 0,
                      rank: 0,
                      consecutiveHits: 0, // 初始化為0，稍後計算連續拖出次數
                    };

                    statResults.set(key2, newStat);
                  }

                  // 追蹤這個組合命中的期號
                  const fullKey = `${firstGroup.join(',')}-${secondGroup.join(
                    ','
                  )}-${gap}-${targetGap}-${targetGroup.join(',')}`;
                  const targetPeriod = results[k].period;

                  if (!hitDetails.has(fullKey)) {
                    hitDetails.set(fullKey, []);
                  }

                  const hitList = hitDetails.get(fullKey);
                  if (hitList) {
                    hitList.push(targetPeriod);
                  }
                }
              }
            }
          }

          progress++;
          postProgress(progress, totalProgress);
        }
      }
    }

    postProgress(progress, totalProgress);

    // 檢查是否還有更多批次需要處理
    if (endIndex < results.length - config.lookAheadCount) {
      setTimeout(() => processBatch(endIndex), 0); // 讓 Worker 釋放資源
    } else {
      finalizeResults();
    }
  }

  function finalizeResults() {
    const results: StatResult[] = [];
    const matchResults: Map<number, StatResult[]> = new Map();

    for (const stat of statResults.values()) {
      const key = `${stat.firstNumbers.join(',')}-${stat.secondNumbers.join(
        ','
      )}-${stat.gap}-${stat.targetGap}`;

      const occurrence = occurrenceResults.get(key);

      if (!occurrence?.isPredict || occurrence.count <= 0) continue;

      // 計算連續拖出次數
      stat.consecutiveHits = calculateConsecutiveHits(stat, occurrence, hitDetails);

      if (stat.consecutiveHits < 1) continue;

      stat.targetProbability = stat.targetMatches / occurrence.count;

      if (!matchResults.has(stat.targetMatches)) {
        matchResults.set(stat.targetMatches, []);
      }
      const matchList = matchResults.get(stat.targetMatches);
      if (matchList) {
        matchList.push(stat);
      }
      results.push(stat);
    }

    const sortedResults = sortAndRankResults(results);

    postMessage({
      type: 'complete',
      data: sortedResults,
      occurrences: occurrenceResults,
      matchData: matchResults,
    });
  }

  processBatch(0);
}

// 計算連續拖出次數
function calculateConsecutiveHits(stat: StatResult, occurrence: Occurrence, hitDetails: Map<string, string[]>): number {
  // 獲取所有有效的期號（排除預測期號）
  const validPeriods = occurrence.periods
    .filter(p => p.targetPeriod !== undefined && p.targetPeriod !== null && p.targetPeriod.trim() !== '')
    .map(p => p.targetPeriod as string);

  if (validPeriods.length === 0) {
    return 0;
  }

  // 將期號按時間順序排序（假設期號是數字格式，數字越大越新）
  const sortedPeriods = validPeriods.sort((a, b) => {
    const numA = parseInt(a);
    const numB = parseInt(b);
    // 檢查解析是否成功
    if (isNaN(numA) || isNaN(numB)) {
      return a.localeCompare(b); // 如果不是數字，使用字符串比較
    }
    return numB - numA; // 降序排列，最新的在前面
  });

  // 獲取這個特定組合的命中期號列表
  const fullKey = `${stat.firstNumbers.join(',')}-${stat.secondNumbers.join(',')}-${stat.gap}-${stat.targetGap}-${stat.targetNumbers.join(',')}`;
  const hitPeriods = hitDetails.get(fullKey) || [];

  if (hitPeriods.length === 0) {
    return 0;
  }

  // 將命中期號轉換為Set以便快速查找
  const hitPeriodsSet = new Set(hitPeriods);

  // 從最新期號開始計算連續拖出次數
  let consecutiveHits = 0;
  for (const period of sortedPeriods) {
    if (hitPeriodsSet.has(period)) {
      consecutiveHits++;
    } else {
      break; // 遇到未拖出就停止計算
    }
  }

  return consecutiveHits;
}

function* generateCombinationsGenerator(
  nums: number[],
  choose: number,
  start = 0,
  current: number[] = []
): Generator<number[]> {
  if (current.length === choose) {
    yield [...current];
    return;
  }
  for (let i = start; i < nums.length; i++) {
    current.push(nums[i]);
    yield* generateCombinationsGenerator(nums, choose, i + 1, current);
    current.pop();
  }
}

// 緩存 function 可選性使用，但可儘量在短生命週期內使用
function getCombinationsGenerator(
  nums: number[],
  size: number
): Generator<number[]> {
  // 直接回傳 generator，不做全量緩存
  return generateCombinationsGenerator(nums, size);
}

function estimateTotalProgress(results: DrawResult[], config: AnalysisConfig) {
  const predictIndex = results.length - 1 + config.lookAheadCount;
  let total = 0;

  for (let i = 0; i < results.length - config.lookAheadCount; i++) {
    for (let j = i + 1; j < results.length && j - i <= config.maxRange; j++) {
      for (let k = j + 1; k < results.length && k - j <= config.maxRange; k++) {
        if (k > predictIndex) break;

        total++;
      }
    }
  }
  return total;
}

function sortAndRankResults(
  results: StatResult[]
): StatResult[] {
  results.sort((a, b) => {
    // 預防 firstGroup 為空的情況，這邊預設若沒有值就設為 Infinity (較後)
    const aFirst =
      a.firstNumbers && a.firstNumbers.length > 0
        ? a.firstNumbers[0]
        : Infinity;
    const bFirst =
      b.firstNumbers && b.firstNumbers.length > 0
        ? b.firstNumbers[0]
        : Infinity;
    return aFirst - bFirst;
  });

  // 加上 rank 屬性 (依照排序後的索引位置)
  return results.map((result, index) => {
    result.rank = index + 1;
    return result;
  });
}

function postProgress(progress: number, total: number) {
  const info: ProgressInfo = {
    stage: 'processing',
    progress,
    total,
  };
  postMessage({ type: 'progress', data: info });
}
