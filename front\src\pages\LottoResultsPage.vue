<template>
  <q-page class="justify-center q-pa-md">
    <div class="text-center q-mb-lg">
      <h4 class="text-h4 text-weight-bold q-my-md">最新開獎結果</h4>
    </div>

    <!-- 載入中狀態 -->
    <div v-if="loading" class="text-center q-py-xl">
      <q-spinner-dots size="50px" color="primary" />
      <div class="text-h6 q-mt-md">載入中...</div>
    </div>

    <!-- 開獎結果卡片 -->
    <div v-else class="results-container">
      <q-card
        v-for="drawType in sortedDrawTypes"
        :key="drawType"
        class="lotto-result-card cursor-pointer q-mb-lg"
        @click="goToLottoDetail(drawType)"
      >
        <q-card-section class="q-pa-xl">
          <div class="row items-center">

            <div class="col-12 col-md-3 text-center q-mb-md">
              <!-- 彩種名稱 -->
              <div class="lotto-type-name text-weight-bold q-mb-md">
                {{ getLottoTypeName(drawType) }}
              </div>
                <!-- 期號和日期 -->
              <div class="period-info">
                <div class="period-number text-weight-bold text-black">
                  第 {{ latestResults[drawType].period }} 期
                </div>
                <div class="draw-date text-grey-7">
                  {{ latestResults[drawType].draw_date }}
                </div>
              </div>
            </div>

            <!-- 開獎號碼 -->
            <div class="col-12 col-md-8 q-mb-md">
              <div class="numbers-section">
                <div class="row justify-center q-gutter-sm">
                  <div
                    v-for="number in latestResults[drawType].draw_number_size"
                    :key="number"
                    class="col-auto"
                  >
                    <div class="ball">
                      {{ paddingZero(number) }}
                    </div>
                  </div>
                  <div
                    v-if="latestResults[drawType].special_number"
                    class="col-auto"
                  >
                    <div class="ball special-number">
                      {{ paddingZero(latestResults[drawType].special_number) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 查看更多按鈕 -->
            <div class="col-12 col-md-1 text-center">
              <q-btn
                icon="arrow_forward"
                color="primary"
                round
                size="lg"
                class="view-more-btn"
                @click.stop="goToLottoDetail(drawType)"
              >
                <q-tooltip>查看歷史記錄</q-tooltip>
              </q-btn>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- 無資料狀態 -->
    <div v-if="!loading && Object.keys(latestResults).length === 0" class="text-center q-py-xl">
      <q-icon name="info" size="60px" color="grey-5" />
      <div class="text-h6 q-mt-md text-grey-7">暫無開獎資料</div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { LOTTO_API } from '@/api';
import { LottoItem } from '@/api/modules/lotto';
import { paddingZero } from '@/utils';

defineOptions({
  name: 'LottoResultsPage',
});

const router = useRouter();

const loading = ref(true);
const latestResults = ref<Record<string, LottoItem>>({});

// 定義彩種排序順序：威力彩、大樂透、539、六合彩
const lottoOrder = ['super_lotto638', 'lotto649', 'daily539', 'lotto_hk'];

// 按照指定順序排序的彩種列表
const sortedDrawTypes = computed(() => {
  return lottoOrder.filter(drawType => latestResults.value[drawType]);
});

// 獲取彩種名稱
const getLottoTypeName = (drawType: string): string => {
  switch (drawType) {
    case 'super_lotto638':
      return '威力彩';
    case 'lotto649':
      return '大樂透';
    case 'daily539':
      return '今彩539';
    case 'lotto_hk':
      return '六合彩';
    default:
      return '';
  }
};

// 獲取最新開獎結果
const fetchLatestResults = async () => {
  try {
    loading.value = true;
    const { data } = await LOTTO_API.getLatestResults();
    latestResults.value = data;
  } catch (error) {
    console.error('獲取最新開獎結果失敗:', error);
  } finally {
    loading.value = false;
  }
};

// 跳轉到彩種詳細頁面
const goToLottoDetail = (drawType: string) => {
  router.push(`/lotto-detail/${drawType}`);
};

onMounted(() => {
  fetchLatestResults();
});
</script>

<style lang="scss" scoped>
.results-container {
  max-width: 1200px;
  margin: 0 auto;
}

.lotto-result-card {
  background-color: #fff;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-color: #3460f2;
  }

  &::before {
    content: '';
    width: 5px;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    background-color: #da8359;
  }
}

.lotto-type-name {
  color: #2c3e50;
  font-size: 2rem;
}

.period-info {
  .period-number {
    font-size: 1.6rem;
  }

  .draw-date {
    font-size: 1.3rem;
    margin-top: 0.5rem;
  }
}

.numbers-section {
  .ball {
    width: 4rem;
    height: 4rem;
    font-size: 1.6rem;
    margin: 0.3rem;
  }
}

.view-more-btn {
  width: 60px;
  height: 60px;
  font-size: 1.8rem;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(52, 96, 242, 0.3);
  }
}

// 響應式設計
@media (max-width: 1199px) {
  .lotto-type-name {
    font-size: 1.8rem;
  }

  .period-info {
    .period-number {
      font-size: 1.5rem;
    }

    .draw-date {
      font-size: 1.2rem;
    }
  }

  .numbers-section {
    .ball {
      width: 3.8rem;
      height: 3.8rem;
      font-size: 1.5rem;
    }
  }
}

@media (max-width: 768px) {
  .lotto-type-name {
    font-size: 1.6rem;
  }

  .period-info {
    .period-number {
      font-size: 1.4rem;
    }

    .draw-date {
      font-size: 1.1rem;
    }
  }

  .numbers-section {
    .ball {
      width: 3.5rem;
      height: 3.5rem;
      font-size: 1.4rem;
    }
  }

  .view-more-btn {
    width: 55px;
    height: 55px;
    font-size: 1.6rem;
  }
}

@media (max-width: 480px) {
  .lotto-type-name {
    font-size: 1.4rem;
  }

  .period-info {
    .period-number {
      font-size: 1.2rem;
    }

    .draw-date {
      font-size: 1rem;
    }
  }

  .numbers-section {
    .ball {
      width: 3rem;
      height: 3rem;
      font-size: 1.2rem;
      margin: 0.2rem;
    }
  }

  .view-more-btn {
    width: 50px;
    height: 50px;
    font-size: 1.4rem;
  }
}
</style>
